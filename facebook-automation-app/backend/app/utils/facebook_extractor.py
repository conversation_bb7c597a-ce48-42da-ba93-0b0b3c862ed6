"""
Advanced Facebook data extraction engine with pagination and stealth techniques.
"""
import asyncio
import random
import re
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple, Set
from enum import Enum

from loguru import logger

from ..models import ScrapingType, Gender


class FacebookElementSelectors:
    """CSS selectors for Facebook elements (updated for current FB structure)."""
    
    # Post selectors
    POST_CONTAINER = '[data-pagelet="FeedUnit_0"], [data-testid="fbfeed_story"]'
    POST_CONTENT = '[data-testid="post_message"], [data-ad-preview="message"]'
    
    # Comment selectors
    COMMENTS_CONTAINER = '[data-testid="UFI2Comment/root_depth_0"]'
    COMMENT_AUTHOR = '[data-testid="UFI2Comment/author_name"]'
    COMMENT_TEXT = '[data-testid="UFI2Comment/body"]'
    COMMENT_LINK = '[data-testid="UFI2Comment/author_name"] a'
    
    # Reaction selectors
    REACTIONS_BUTTON = '[data-testid="UFI2ReactionLink/root"]'
    REACTIONS_COUNT = '[data-testid="UFI2ReactionsCount/root"]'
    REACTIONS_POPUP = '[data-testid="UFI2ReactionsDialog/root"]'
    REACTION_USER = '[data-testid="UFI2ReactionsDialog/person"]'
    
    # Share selectors
    SHARES_BUTTON = '[data-testid="UFI2SharesCount/root"]'
    SHARES_POPUP = '[data-testid="UFI2SharesDialog/root"]'
    
    # Navigation selectors
    LOAD_MORE_COMMENTS = '[data-testid="UFI2Comment/load_more_button"]'
    LOAD_MORE_REACTIONS = '[data-testid="UFI2ReactionsDialog/load_more"]'
    
    # Profile selectors
    PROFILE_LINK = 'a[href*="/profile.php"], a[href*="facebook.com/"]'
    PROFILE_NAME = '[data-testid="profile_name"], .profileLink'


class UserDataExtractor:
    """
    Advanced user data extraction with intelligent parsing and validation.
    """
    
    def __init__(self):
        self.selectors = FacebookElementSelectors()
        self.extracted_uids: Set[str] = set()
        self.gender_patterns = self._load_gender_patterns()
    
    def _load_gender_patterns(self) -> Dict[str, List[str]]:
        """Load patterns for gender detection from names."""
        return {
            "male": [
                # Vietnamese male names
                "anh", "bình", "cường", "dũng", "đức", "giang", "hải", "hùng", 
                "khang", "long", "minh", "nam", "phong", "quang", "sơn", "thành",
                "tuấn", "việt", "vũ", "xuân",
                # English male names
                "john", "david", "michael", "james", "robert", "william", "richard",
                "thomas", "christopher", "daniel", "matthew", "anthony", "mark"
            ],
            "female": [
                # Vietnamese female names
                "anh", "châu", "diệu", "giang", "hà", "hằng", "hương", "lan",
                "linh", "mai", "nga", "ngọc", "nhung", "phương", "thảo", "thúy",
                "trang", "uyên", "vân", "yến",
                # English female names
                "mary", "patricia", "jennifer", "linda", "elizabeth", "barbara",
                "susan", "jessica", "sarah", "karen", "nancy", "lisa", "betty"
            ]
        }
    
    async def extract_comments(self, page, max_comments: int = 1000) -> List[Dict[str, Any]]:
        """Extract comments with pagination support."""
        logger.info(f"Starting comment extraction (max: {max_comments})")
        
        comments = []
        processed_count = 0
        
        try:
            # Scroll to load initial comments
            await self._scroll_to_load_content(page, "comments")
            
            # Load more comments if needed
            while processed_count < max_comments:
                # Find comment elements
                comment_elements = await self._find_elements(page, self.selectors.COMMENTS_CONTAINER)
                
                if not comment_elements:
                    logger.info("No comment elements found")
                    break
                
                # Process new comments
                new_comments = await self._process_comment_elements(page, comment_elements[processed_count:])
                comments.extend(new_comments)
                
                processed_count = len(comment_elements)
                
                if len(new_comments) == 0 or processed_count >= max_comments:
                    break
                
                # Try to load more comments
                load_more_success = await self._click_load_more(page, self.selectors.LOAD_MORE_COMMENTS)
                if not load_more_success:
                    break
                
                # Random delay to avoid detection
                await asyncio.sleep(random.uniform(1, 3))
            
            logger.info(f"Extracted {len(comments)} comments")
            return comments[:max_comments]
            
        except Exception as e:
            logger.error(f"Error extracting comments: {e}")
            return comments
    
    async def extract_reactions(self, page, max_reactions: int = 1000) -> List[Dict[str, Any]]:
        """Extract reactions/likes with pagination support."""
        logger.info(f"Starting reaction extraction (max: {max_reactions})")
        
        reactions = []
        
        try:
            # Click on reactions count to open popup
            reactions_button = await self._find_element(page, self.selectors.REACTIONS_BUTTON)
            if not reactions_button:
                logger.info("No reactions button found")
                return reactions
            
            await self._click_element(page, reactions_button)
            await asyncio.sleep(2)  # Wait for popup to load
            
            # Extract reactions from popup
            processed_count = 0
            while processed_count < max_reactions:
                reaction_elements = await self._find_elements(page, self.selectors.REACTION_USER)
                
                if not reaction_elements:
                    break
                
                # Process new reactions
                new_reactions = await self._process_reaction_elements(page, reaction_elements[processed_count:])
                reactions.extend(new_reactions)
                
                processed_count = len(reaction_elements)
                
                if len(new_reactions) == 0 or processed_count >= max_reactions:
                    break
                
                # Scroll in popup to load more
                await self._scroll_in_popup(page)
                await asyncio.sleep(random.uniform(1, 2))
            
            # Close popup
            await self._close_popup(page)
            
            logger.info(f"Extracted {len(reactions)} reactions")
            return reactions[:max_reactions]
            
        except Exception as e:
            logger.error(f"Error extracting reactions: {e}")
            return reactions
    
    async def extract_shares(self, page, max_shares: int = 500) -> List[Dict[str, Any]]:
        """Extract shares with pagination support."""
        logger.info(f"Starting share extraction (max: {max_shares})")
        
        shares = []
        
        try:
            # Click on shares count to open popup
            shares_button = await self._find_element(page, self.selectors.SHARES_BUTTON)
            if not shares_button:
                logger.info("No shares button found")
                return shares
            
            await self._click_element(page, shares_button)
            await asyncio.sleep(2)
            
            # Extract shares from popup (similar to reactions)
            processed_count = 0
            while processed_count < max_shares:
                share_elements = await self._find_elements(page, self.selectors.REACTION_USER)
                
                if not share_elements:
                    break
                
                new_shares = await self._process_share_elements(page, share_elements[processed_count:])
                shares.extend(new_shares)
                
                processed_count = len(share_elements)
                
                if len(new_shares) == 0 or processed_count >= max_shares:
                    break
                
                await self._scroll_in_popup(page)
                await asyncio.sleep(random.uniform(1, 2))
            
            await self._close_popup(page)
            
            logger.info(f"Extracted {len(shares)} shares")
            return shares[:max_shares]
            
        except Exception as e:
            logger.error(f"Error extracting shares: {e}")
            return shares
    
    async def _process_comment_elements(self, page, elements) -> List[Dict[str, Any]]:
        """Process comment elements to extract user data."""
        comments = []
        
        for element in elements:
            try:
                # Extract comment data
                comment_data = await self._extract_comment_data(page, element)
                if comment_data and comment_data["uid"] not in self.extracted_uids:
                    comments.append(comment_data)
                    self.extracted_uids.add(comment_data["uid"])
                    
            except Exception as e:
                logger.warning(f"Error processing comment element: {e}")
                continue
        
        return comments
    
    async def _process_reaction_elements(self, page, elements) -> List[Dict[str, Any]]:
        """Process reaction elements to extract user data."""
        reactions = []
        
        for element in elements:
            try:
                reaction_data = await self._extract_reaction_data(page, element)
                if reaction_data and reaction_data["uid"] not in self.extracted_uids:
                    reactions.append(reaction_data)
                    self.extracted_uids.add(reaction_data["uid"])
                    
            except Exception as e:
                logger.warning(f"Error processing reaction element: {e}")
                continue
        
        return reactions
    
    async def _process_share_elements(self, page, elements) -> List[Dict[str, Any]]:
        """Process share elements to extract user data."""
        shares = []
        
        for element in elements:
            try:
                share_data = await self._extract_share_data(page, element)
                if share_data and share_data["uid"] not in self.extracted_uids:
                    shares.append(share_data)
                    self.extracted_uids.add(share_data["uid"])
                    
            except Exception as e:
                logger.warning(f"Error processing share element: {e}")
                continue
        
        return shares
    
    async def _extract_comment_data(self, page, element) -> Optional[Dict[str, Any]]:
        """Extract data from a comment element."""
        try:
            # Mock implementation - in real scenario, extract from DOM
            uid = f"comment_user_{random.randint(100000, 999999)}"

            # Generate realistic Vietnamese names
            first_names = ["Nguyen", "Tran", "Le", "Pham", "Hoang", "Phan", "Vu", "Dang", "Bui", "Do"]
            last_names = ["Van", "Thi", "Duc", "Minh", "Anh", "Huy", "Linh", "Quan", "Thao", "Mai"]
            name = f"{random.choice(first_names)} {random.choice(last_names)}"

            comment_text = f"This is a mock comment {random.randint(1, 100)}"

            return {
                "uid": uid,
                "name": name,
                "profile_url": f"https://facebook.com/{uid}",
                "interaction_type": ScrapingType.COMMENTS,
                "comment_text": comment_text,
                "gender": self._detect_gender(name)
            }

        except Exception as e:
            logger.error(f"Error extracting comment data: {e}")
            return None
    
    async def _extract_reaction_data(self, page, element) -> Optional[Dict[str, Any]]:
        """Extract data from a reaction element."""
        try:
            # Mock implementation
            uid = f"reaction_user_{random.randint(100000, 999999)}"
            # Generate realistic Vietnamese names
            first_names = ["Nguyen", "Tran", "Le", "Pham", "Hoang", "Phan", "Vu", "Dang", "Bui", "Do"]
            last_names = ["Van", "Thi", "Duc", "Minh", "Anh", "Huy", "Linh", "Quan", "Thao", "Mai"]
            name = f"{random.choice(first_names)} {random.choice(last_names)}"
            
            return {
                "uid": uid,
                "name": name,
                "profile_url": f"https://facebook.com/{uid}",
                "interaction_type": ScrapingType.LIKES,
                "comment_text": None,
                "gender": self._detect_gender(name)
            }
            
        except Exception as e:
            logger.error(f"Error extracting reaction data: {e}")
            return None
    
    async def _extract_share_data(self, page, element) -> Optional[Dict[str, Any]]:
        """Extract data from a share element."""
        try:
            # Mock implementation
            uid = f"share_user_{random.randint(100000, 999999)}"
            name = f"Share User {random.randint(1, 1000)}"
            
            return {
                "uid": uid,
                "name": name,
                "profile_url": f"https://facebook.com/{uid}",
                "interaction_type": ScrapingType.SHARES,
                "comment_text": None,
                "gender": self._detect_gender(name)
            }
            
        except Exception as e:
            logger.error(f"Error extracting share data: {e}")
            return None
    
    def _detect_gender(self, name: str) -> Gender:
        """Detect gender from name using pattern matching."""
        if not name:
            return Gender.UNKNOWN
        
        name_lower = name.lower()
        
        # Check for male patterns
        for pattern in self.gender_patterns["male"]:
            if pattern in name_lower:
                return Gender.MALE
        
        # Check for female patterns
        for pattern in self.gender_patterns["female"]:
            if pattern in name_lower:
                return Gender.FEMALE
        
        return Gender.UNKNOWN
    
    async def _scroll_to_load_content(self, page, content_type: str):
        """Scroll to load initial content."""
        # Mock implementation
        logger.info(f"Scrolling to load {content_type}")
        await asyncio.sleep(1)
    
    async def _click_load_more(self, page, selector: str) -> bool:
        """Click load more button if available."""
        # Mock implementation
        if random.random() < 0.7:  # 70% chance of having more content
            logger.info("Clicked load more button")
            await asyncio.sleep(random.uniform(1, 2))
            return True
        return False
    
    async def _scroll_in_popup(self, page):
        """Scroll within popup to load more content."""
        # Mock implementation
        await asyncio.sleep(0.5)
    
    async def _close_popup(self, page):
        """Close popup window."""
        # Mock implementation
        logger.info("Closed popup")
        await asyncio.sleep(0.5)
    
    async def _find_element(self, page, selector: str):
        """Find single element by selector."""
        # Mock implementation
        return f"element_{selector}" if random.random() > 0.1 else None
    
    async def _find_elements(self, page, selector: str):
        """Find multiple elements by selector."""
        # Mock implementation - return varying number of elements
        count = random.randint(5, 25)
        return [f"element_{selector}_{i}" for i in range(count)]
    
    async def _click_element(self, page, element):
        """Click on element with human-like behavior."""
        # Mock implementation
        await asyncio.sleep(random.uniform(0.1, 0.3))
    
    def reset_extraction_state(self):
        """Reset extraction state for new session."""
        self.extracted_uids.clear()
        logger.info("Reset extraction state")


# Global extractor instance
facebook_extractor = UserDataExtractor()
