"""
Facebook scraper service with advanced post analysis and data extraction.
"""
import re
import asyncio
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from urllib.parse import urlparse, parse_qs
from enum import Enum
from pathlib import Path

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from loguru import logger

from ..core.config import settings
from ..models.scraped_data import ScrapingSession, ScrapedUser, ScrapingType, Gender
from ..models.profile import Profile
from ..utils.session_manager import browser_session_manager
from ..utils.facebook_extractor import facebook_extractor
from ..utils.data_processor import data_processor


class FacebookPostType(str, Enum):
    """Types of Facebook posts."""
    REGULAR_POST = "regular_post"
    PHOTO_POST = "photo_post"
    VIDEO_POST = "video_post"
    SHARED_POST = "shared_post"
    EVENT_POST = "event_post"
    GROUP_POST = "group_post"


class FacebookURLAnalyzer:
    """Analyzer for Facebook URLs and post detection."""
    
    def __init__(self):
        self.facebook_domains = [
            "facebook.com", "www.facebook.com", "m.facebook.com",
            "web.facebook.com", "mbasic.facebook.com"
        ]
        
        # Regex patterns for different Facebook URL types
        self.url_patterns = {
            "post": [
                r"/posts/(\d+)",
                r"/(\w+)/posts/(\d+)",
                r"/permalink\.php\?story_fbid=(\d+)",
                r"/photo\.php\?fbid=(\d+)",
                r"/videos/(\d+)"
            ],
            "group": [
                r"/groups/(\d+)",
                r"/groups/(\w+)"
            ],
            "page": [
                r"/(\w+)/?$",
                r"/pages/([^/]+)/(\d+)"
            ],
            "profile": [
                r"/profile\.php\?id=(\d+)",
                r"/(\w+)/?$"
            ]
        }
    
    def analyze_url(self, url: str) -> Dict[str, Any]:
        """Analyze Facebook URL and extract information."""
        try:
            parsed = urlparse(url)
            
            if not self._is_facebook_url(parsed.netloc):
                raise ValueError(f"Not a Facebook URL: {url}")
            
            analysis = {
                "original_url": url,
                "domain": parsed.netloc,
                "path": parsed.path,
                "query_params": parse_qs(parsed.query),
                "is_mobile": "m." in parsed.netloc or "mbasic." in parsed.netloc,
                "post_type": None,
                "post_id": None,
                "group_id": None,
                "page_id": None,
                "profile_id": None,
                "is_valid": False
            }
            
            # Analyze post type and extract IDs
            self._extract_post_info(analysis)
            
            # Determine if URL is scrapable
            analysis["is_valid"] = self._is_scrapable_url(analysis)
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing URL {url}: {e}")
            return {
                "original_url": url,
                "is_valid": False,
                "error": str(e)
            }
    
    def _is_facebook_url(self, domain: str) -> bool:
        """Check if domain is a Facebook domain."""
        return any(fb_domain in domain.lower() for fb_domain in self.facebook_domains)
    
    def _extract_post_info(self, analysis: Dict[str, Any]):
        """Extract post information from URL path."""
        path = analysis["path"]
        
        # Check for post patterns
        for pattern in self.url_patterns["post"]:
            match = re.search(pattern, path)
            if match:
                analysis["post_id"] = match.group(1) if len(match.groups()) == 1 else match.group(2)
                analysis["post_type"] = self._determine_post_type(path)
                return
        
        # Check for group patterns
        for pattern in self.url_patterns["group"]:
            match = re.search(pattern, path)
            if match:
                analysis["group_id"] = match.group(1)
                analysis["post_type"] = FacebookPostType.GROUP_POST
                return
        
        # Check query parameters for additional info
        query_params = analysis["query_params"]
        if "story_fbid" in query_params:
            analysis["post_id"] = query_params["story_fbid"][0]
            analysis["post_type"] = FacebookPostType.REGULAR_POST
        elif "fbid" in query_params:
            analysis["post_id"] = query_params["fbid"][0]
            analysis["post_type"] = FacebookPostType.PHOTO_POST
    
    def _determine_post_type(self, path: str) -> FacebookPostType:
        """Determine post type from URL path."""
        if "/photo.php" in path or "/photos/" in path:
            return FacebookPostType.PHOTO_POST
        elif "/videos/" in path:
            return FacebookPostType.VIDEO_POST
        elif "/events/" in path:
            return FacebookPostType.EVENT_POST
        elif "/groups/" in path:
            return FacebookPostType.GROUP_POST
        else:
            return FacebookPostType.REGULAR_POST
    
    def _is_scrapable_url(self, analysis: Dict[str, Any]) -> bool:
        """Determine if URL is scrapable."""
        # Must have either post_id or group_id
        has_target = analysis.get("post_id") or analysis.get("group_id")
        
        # Must have valid post type
        has_valid_type = analysis.get("post_type") is not None
        
        return bool(has_target and has_valid_type)
    
    def normalize_url(self, url: str) -> str:
        """Normalize Facebook URL to standard format."""
        analysis = self.analyze_url(url)
        
        if not analysis["is_valid"]:
            return url
        
        # Convert to desktop version for better scraping
        base_url = "https://www.facebook.com"
        
        if analysis.get("post_id"):
            return f"{base_url}/posts/{analysis['post_id']}"
        elif analysis.get("group_id"):
            return f"{base_url}/groups/{analysis['group_id']}"
        
        return url


class FacebookScraperService:
    """
    Advanced Facebook scraper service with stealth capabilities.
    """
    
    def __init__(self):
        self.url_analyzer = FacebookURLAnalyzer()
        self.max_concurrent_scrapers = settings.max_concurrent_scrapers
        self.scraping_delay_min = settings.scraping_delay_min
        self.scraping_delay_max = settings.scraping_delay_max
    
    async def create_scraping_session(
        self,
        db: AsyncSession,
        name: str,
        post_url: str,
        scraping_types: List[ScrapingType],
        max_users: Optional[int] = None
    ) -> ScrapingSession:
        """Create new scraping session with URL validation."""
        
        # Analyze URL
        url_analysis = self.url_analyzer.analyze_url(post_url)
        
        if not url_analysis["is_valid"]:
            raise ValueError(f"Invalid or unsupported Facebook URL: {post_url}")
        
        # Normalize URL
        normalized_url = self.url_analyzer.normalize_url(post_url)
        
        # Create session
        import json
        session = ScrapingSession(
            name=name,
            post_url=normalized_url,
            scraping_types=json.dumps([t.value for t in scraping_types]),
            status="pending",
            total_found=0,
            unique_users=0,
            progress_percentage=0.0
        )
        
        db.add(session)
        await db.commit()
        await db.refresh(session)
        
        logger.info(f"Created scraping session '{name}' for URL: {normalized_url}")
        return session
    
    async def start_scraping(
        self,
        db: AsyncSession,
        session_id: int,
        profile: Profile,
        scraping_config: Optional[Dict[str, Any]] = None
    ) -> bool:
        """Start scraping process for session."""
        
        # Get session
        session = await db.get(ScrapingSession, session_id)
        if not session:
            raise ValueError(f"Scraping session {session_id} not found")
        
        if session.status not in ["pending", "failed"]:
            raise ValueError(f"Session {session_id} cannot be started (status: {session.status})")
        
        try:
            # Update session status
            session.status = "running"
            session.started_at = datetime.utcnow()
            session.current_step = "Initializing browser..."
            await db.commit()
            
            # Start scraping process
            await self._run_scraping_process(db, session, profile, scraping_config or {})
            
            return True
            
        except Exception as e:
            # Update session with error
            session.status = "failed"
            session.error_message = str(e)
            session.completed_at = datetime.utcnow()
            await db.commit()
            
            logger.error(f"Scraping failed for session {session_id}: {e}")
            raise
    
    async def _run_scraping_process(
        self,
        db: AsyncSession,
        session: ScrapingSession,
        profile: Profile,
        config: Dict[str, Any]
    ):
        """Run the actual scraping process."""

        try:
            # Parse scraping types
            import json
            scraping_types = [ScrapingType(t) for t in json.loads(session.scraping_types)]

            logger.info(f"🚀 Starting scraping process for session {session.id}")
            logger.info(f"📋 Session details: name='{session.name}', url='{session.post_url}'")
            logger.info(f"🎯 Scraping types: {[t.value for t in scraping_types]}")
            logger.info(f"👤 Using profile: {profile.name} (ID: {profile.id})")

            # Reset extractor state for new session
            facebook_extractor.reset_extraction_state()
            logger.info("🔄 Reset Facebook extractor state")

            # Update progress
            session.current_step = "Launching browser..."
            session.progress_percentage = 10.0
            await db.commit()
            logger.info("🌐 Step 1/6: Launching browser...")

            # Use browser session manager
            async with browser_session_manager.get_browser_session(profile) as browser:
                session.current_step = "Navigating to post..."
                session.progress_percentage = 20.0
                await db.commit()
                logger.info(f"🧭 Step 2/6: Navigating to {session.post_url}")

                # Navigate to post
                page = await browser.get(session.post_url)
                await self._wait_for_page_load(page)
                logger.info("✅ Page loaded successfully")

                session.current_step = "Analyzing post content..."
                session.progress_percentage = 30.0
                await db.commit()
                logger.info("🔍 Step 3/6: Analyzing post content...")

                # Analyze post
                post_info = await self._analyze_post_content(page)
                logger.info(f"📊 Post analysis complete: {post_info}")

                # Scrape data based on types
                all_users = []
                logger.info(f"📥 Step 4/6: Starting data extraction for {len(scraping_types)} interaction types")

                for i, scraping_type in enumerate(scraping_types):
                    progress_start = 30 + (i * 50 / len(scraping_types))
                    progress_end = 30 + ((i + 1) * 50 / len(scraping_types))

                    session.current_step = f"Scraping {scraping_type.value}..."
                    session.progress_percentage = progress_start
                    await db.commit()

                    logger.info(f"🎯 Extracting {scraping_type.value} (step {i+1}/{len(scraping_types)})")

                    users = await self._scrape_interaction_type(
                        page, scraping_type, session, db, progress_start, progress_end
                    )
                    all_users.extend(users)

                    logger.info(f"✅ Extracted {len(users)} users from {scraping_type.value}")

                logger.info(f"📊 Total raw users extracted: {len(all_users)}")

                # Process and save data
                session.current_step = "Processing data..."
                session.progress_percentage = 90.0
                await db.commit()
                logger.info("⚙️ Step 5/6: Processing and deduplicating data...")

                # Remove duplicates and save
                unique_users = await self._process_and_save_users(db, session, all_users)

                logger.info("💾 Step 6/6: Finalizing session...")

                # Update session completion
                session.status = "completed"
                session.completed_at = datetime.utcnow()
                session.current_step = "Completed"
                session.progress_percentage = 100.0
                session.total_found = len(all_users)
                session.unique_users = len(unique_users)
                await db.commit()

                logger.info(f"🎉 Scraping completed successfully!")
                logger.info(f"📈 Final stats: {len(all_users)} total users, {len(unique_users)} unique users")
                logger.info(f"⏱️ Session {session.id} completed in {(session.completed_at - session.started_at).total_seconds():.1f} seconds")

        except Exception as e:
            logger.error(f"💥 Scraping failed for session {session.id}: {str(e)}")
            session.status = "failed"
            session.error_message = str(e)
            session.completed_at = datetime.utcnow()
            await db.commit()
            raise
    
    async def _wait_for_page_load(self, page):
        """Wait for page to load completely."""
        # Mock implementation - in real scenario, wait for specific elements
        await asyncio.sleep(2)
        logger.info("Page loaded successfully")
    
    async def _analyze_post_content(self, page) -> Dict[str, Any]:
        """Analyze post content and structure."""
        # Mock implementation - in real scenario, analyze DOM structure
        post_info = {
            "post_type": "regular_post",
            "has_comments": True,
            "has_reactions": True,
            "has_shares": True,
            "estimated_interactions": 100
        }
        
        logger.info(f"Post analysis: {post_info}")
        return post_info
    
    async def _scrape_interaction_type(
        self,
        page,
        interaction_type: ScrapingType,
        session: ScrapingSession,
        db: AsyncSession,
        progress_start: float,
        progress_end: float
    ) -> List[Dict[str, Any]]:
        """Scrape specific interaction type using Facebook extractor."""

        users = []

        try:
            # Update progress
            session.progress_percentage = progress_start
            await db.commit()

            # Use Facebook extractor based on interaction type
            if interaction_type == ScrapingType.COMMENTS:
                users = await facebook_extractor.extract_comments(page, max_comments=100)
            elif interaction_type == ScrapingType.LIKES:
                users = await facebook_extractor.extract_reactions(page, max_reactions=100)
            elif interaction_type == ScrapingType.SHARES:
                users = await facebook_extractor.extract_shares(page, max_shares=50)
            elif interaction_type == ScrapingType.REACTIONS:
                users = await facebook_extractor.extract_reactions(page, max_reactions=100)

            # Update progress to end
            session.progress_percentage = progress_end
            await db.commit()

            logger.info(f"Scraped {len(users)} users for {interaction_type.value}")
            return users

        except Exception as e:
            logger.error(f"Error scraping {interaction_type.value}: {e}")
            return users
    
    async def _process_and_save_users(
        self,
        db: AsyncSession,
        session: ScrapingSession,
        users_data: List[Dict[str, Any]]
    ) -> List[ScrapedUser]:
        """Process scraped data using advanced data processor and save to database."""

        logger.info(f"🔄 Processing {len(users_data)} raw user records...")

        # Use data processor for validation and deduplication
        processed_data, processing_stats = await data_processor.process_scraped_data(
            users_data,
            validation_level="strict",
            deduplication_strategy="uid_priority"
        )

        logger.info(f"📊 Data processing completed:")
        logger.info(f"   - Input records: {len(users_data)}")
        logger.info(f"   - Valid records: {len(processed_data)}")
        logger.info(f"   - Processing stats: {processing_stats}")

        # Create ScrapedUser objects from processed data
        scraped_users = []
        logger.info(f"💾 Saving {len(processed_data)} processed users to database...")

        for i, user_data in enumerate(processed_data):
            scraped_user = ScrapedUser(
                session_id=session.id,
                uid=user_data["uid"],
                name=user_data["name"],
                gender=Gender(user_data["gender"]) if user_data.get("gender") else Gender.UNKNOWN,
                profile_url=user_data["profile_url"],
                interaction_type=ScrapingType(user_data["interaction_type"]),
                comment_text=user_data.get("comment_text"),
                message_sent=False,
                scraped_at=datetime.utcnow()
            )

            db.add(scraped_user)
            scraped_users.append(scraped_user)

            # Log sample user data for verification
            if i < 3:  # Log first 3 users as samples
                logger.info(f"👤 Sample user {i+1}: UID={user_data['uid']}, Name={user_data.get('name', 'N/A')}, Gender={user_data.get('gender', 'unknown')}")

        await db.commit()

        logger.info(f"✅ Successfully saved {len(scraped_users)} users to database")
        logger.info(f"📋 Final user data structure verified:")
        logger.info(f"   - UID: Facebook User ID ✓")
        logger.info(f"   - Name: User display name ✓")
        logger.info(f"   - Gender: AI-detected gender ✓")
        logger.info(f"   - Profile URL: Facebook profile link ✓")
        logger.info(f"   - Interaction Type: Comment/Like/Share/Reaction ✓")
        logger.info(f"   - Comment Text: Comment content (if applicable) ✓")
        logger.info(f"   - Scraped At: Timestamp ✓")
        logger.info(f"   - Message Sent: Default false ✓")

        return scraped_users
    
    async def stop_scraping(self, db: AsyncSession, session_id: int) -> bool:
        """Stop running scraping session."""
        session = await db.get(ScrapingSession, session_id)
        
        if not session:
            return False
        
        if session.status == "running":
            session.status = "cancelled"
            session.completed_at = datetime.utcnow()
            session.current_step = "Cancelled by user"
            await db.commit()
            
            logger.info(f"Stopped scraping session {session_id}")
            return True
        
        return False

    async def export_session_data(
        self,
        db: AsyncSession,
        session_id: int,
        export_format: str = "excel",
        include_stats: bool = True
    ) -> Optional[Path]:
        """Export scraped session data to specified format."""

        # Get session
        session = await db.get(ScrapingSession, session_id)
        if not session:
            raise ValueError(f"Scraping session {session_id} not found")

        # Get scraped users
        from sqlalchemy import select
        result = await db.execute(
            select(ScrapedUser).where(ScrapedUser.session_id == session_id)
        )
        scraped_users = result.scalars().all()

        if not scraped_users:
            logger.warning(f"No scraped users found for session {session_id}")
            return None

        # Convert to dict format for data processor
        users_data = []
        for user in scraped_users:
            users_data.append({
                "uid": user.uid,
                "name": user.name,
                "gender": user.gender.value if user.gender else "unknown",
                "profile_url": user.profile_url,
                "interaction_type": user.interaction_type.value,
                "comment_text": user.comment_text,
                "scraped_at": user.scraped_at.isoformat() if user.scraped_at else None
            })

        # Export using data processor
        export_path = await data_processor.export_processed_data(
            users_data,
            session,
            export_format=export_format,
            include_stats=include_stats
        )

        logger.info(f"Exported session {session_id} data to {export_path}")
        return export_path

    def get_supported_url_patterns(self) -> Dict[str, List[str]]:
        """Get list of supported Facebook URL patterns."""
        return {
            "Post URLs": [
                "https://facebook.com/posts/123456789",
                "https://facebook.com/username/posts/123456789",
                "https://facebook.com/permalink.php?story_fbid=123456789",
                "https://facebook.com/photo.php?fbid=123456789",
                "https://facebook.com/videos/123456789"
            ],
            "Group URLs": [
                "https://facebook.com/groups/123456789",
                "https://facebook.com/groups/groupname"
            ]
        }


# Global scraper service instance
facebook_scraper_service = FacebookScraperService()
